<div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
    <!-- Waiver Card -->
    @for (waiver of signedWaivers(); track $index) {
    <div class="bg-surface-white p-6 rounded-4xl shadow-lg overflow-hidden">
        <!-- Waiver Code Header -->
        <div class="bg-[#0bab68] text-white p-4 text-center rounded-3xl">
            <p
                i18n="signed-waiver.waiver-code"
                class="text-xs mb-0.5 uppercase"
            >
                Waiver Code
            </p>
            <p i18n="signed-waiver.waiver-code-value" class="text-xl font-bold">
                {{ waiver.WaiverCode }}
            </p>
        </div>

        <!-- Waiver Details -->
        <div class="mt-6">
            <div class="grid grid-cols-2 gap-4 mb-4">
                <div>
                    <p
                        i18n="signed-waiver.signed-by"
                        class="text-xs text-neutral-dark mb-0.5"
                    >
                        Signed By
                    </p>
                    <p
                        i18n="signed-waiver.signed-by-value"
                        class="text-sm font-medium"
                    >
                        {{ waiver.SignedByName || "N/A" }}
                    </p>
                </div>
                <div>
                    <p
                        i18n="signed-waiver.signed-for"
                        class="text-xs text-neutral-dark mb-0.5"
                    >
                        Signed For
                    </p>
                    <p
                        i18n="signed-waiver.signed-for-value"
                        class="text-sm font-medium"
                    >
                        {{ waiver.SignedForName || "N/A" }}
                    </p>
                </div>
            </div>

            <div class="grid grid-cols-2 gap-4 mb-6">
                <div>
                    <p
                        i18n="signed-waiver.signed-date"
                        class="text-xs text-neutral-dark mb-0.5"
                    >
                        Signed Date
                    </p>
                    <p
                        i18n="signed-waiver.signed-date-value"
                        class="text-sm font-medium"
                    >
                        {{ (waiver.SignedDate | date : "dd-MM-yyyy") || "N/A" }}
                    </p>
                </div>
                <div>
                    <p
                        i18n="signed-waiver.expiry-date"
                        class="text-xs text-neutral-dark mb-0.5"
                    >
                        Expiry Date
                    </p>
                    <p
                        i18n="signed-waiver.expiry-date-value"
                        class="text-sm font-medium"
                    >
                        {{ (waiver.ExpiryDate | date : "dd-MM-yyyy") || "N/A" }}
                    </p>
                </div>
            </div>

            <div class="w-full border-b-2 border-surface my-6 opacity-30"></div>
            <!-- Action Links -->
            <div class="grid grid-cols-2 gap-4">
                <button
                    (click)="
                        viewWaiver(waiver.SignedWaiverFileContentInBase64Format)
                    "
                    class="flex items-center text-sm text-secondary-blue gap-2 underline"
                >
                    <img src="/assets/icons/preview-pink.svg" alt="View" />
                    <span i18n="signed-waiver.view-waiver">View Waiver</span>
                </button>

                <a
                    [href]="
                        'data:application/pdf;base64,' +
                        waiver.SignedWaiverFileContentInBase64Format
                    "
                    [download]="waiver.WaiverName || 'Waiver.pdf'"
                    class="flex items-center text-sm text-secondary-blue gap-2 underline"
                >
                    <img
                        src="/assets/icons/download-green.svg"
                        alt="Download"
                    />
                    <span i18n="signed-waiver.download-waiver"
                        >Download Waiver</span
                    >
                </a>
            </div>
        </div>
    </div>
    }
</div>

<lib-modal
    [isOpen]="isModalOpen()"
    [dialogueHeader]="'Signed Waiver'"
    [modalContent]="pdfContent"
    (closeModal)="closeModal()"
>
</lib-modal>

<!-- template for pdf -->
<ng-template #pdfContent>
    @if (currentPdfDataUrl()) {
        <ngx-extended-pdf-viewer
            [src]="currentPdfDataUrl()!"
            [height]="'95vh'"
            [showToolbar]="false"
            [showSidebarButton]="false"
            [showFindButton]="false"
            [showPagingButtons]="false"
            [showZoomButtons]="false"
            [showPresentationModeButton]="false"
            [showOpenFileButton]="false"
            [showPrintButton]="false"
            [showDownloadButton]="false"
            [showSecondaryToolbarButton]="false"
            [showRotateButton]="false"
            [showHandToolButton]="false"
            [showSpreadButton]="false"
            [showPropertiesButton]="false"
            [textLayer]="true"
        >
        </ngx-extended-pdf-viewer>
    } @else {
    <div class="flex items-center justify-center h-full">
        <p class="text-gray-600">No PDF data available</p>
    </div>
    }
</ng-template>
