/**
 * @fileoverview SignedWaiverComponent is a component that displays and views signed waivers in a modal
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-08-12
 */
/**
 * @component SignedWaiverComponent
 * @description
 * Component for displaying and viewing signed waivers in a modal format. This component
 * handles the display of signed waiver information and provides functionality to view
 * waiver PDFs in a modal dialog.
 *
 * @usage
 * This component is used within the my-signed-waivers page to display individual
 * signed waiver items and provide PDF viewing functionality
 *
 * @inputs
 * - signedWaivers: Array of CustomerSignedWaiverDTO objects containing signed waiver data
 *
 * @outputs
 * - No outputs defined in this component
 *
 * @dependencies
 * - DatePipe: Angular pipe for date formatting
 * - ModalComponent: UI component for modal dialogs
 * - DomSanitizer: Angular service for sanitizing URLs
 * - CustomerSignedWaiverDTO: Data transfer object for signed waiver information
 *
 * @methods
 * - viewWaiver(): Opens modal with PDF viewer for the selected waiver
 * - closeModal(): Closes the modal and clears PDF URL
 */
 
import {
    ChangeDetectionStrategy,
    Component,
    input,
    signal,
} from '@angular/core';
import { CustomerSignedWaiverDTO } from 'projects/online-waiver/src/app/models/customer-signed-waiver-dto.model';
import { DatePipe } from '@angular/common';
import { ModalComponent } from 'lib-ui-kit';
import { NgxExtendedPdfViewerModule } from 'ngx-extended-pdf-viewer';
import { NgClass } from "../../../../../../node_modules/@angular/common/common_module.d-NEF7UaHr";
 
/**
 * Component for displaying and viewing signed waivers in a modal
 */
@Component({
    selector: 'app-signed-waiver',
    imports: [DatePipe, ModalComponent, NgxExtendedPdfViewerModule, NgClass],
    templateUrl: './signed-waiver.component.html',
    styleUrl: './signed-waiver.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SignedWaiverComponent {
    signedWaivers = input<CustomerSignedWaiverDTO[]>([]);
 
    isModalOpen = signal(false);
    currentPdfDataUrl = signal<string | null>(null);
 
    /**
     * Opens modal with PDF viewer for the selected waiver
     */
    viewWaiver(base64Data: string | null): void {
        if (!base64Data) {
            console.error('No base64 data provided');
            return;
        }
 
        this.currentPdfDataUrl.set(`data:application/pdf;base64,${base64Data}`);
        this.isModalOpen.set(true);
    }
 
    /**
     * Closes the modal and clears PDF data
     */
    closeModal(): void {
        this.isModalOpen.set(false);
        this.currentPdfDataUrl.set(null);
    }
}